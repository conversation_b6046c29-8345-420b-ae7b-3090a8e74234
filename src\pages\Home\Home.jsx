import React from "react";
import Particles from "../../components/Particles";
import Aurora from "../../components/Aurora";
import { useTheme } from "../../context/ThemeContext";
import { Phone, Download } from "lucide-react";
import mainImage from "../../assets/logo.png";
function Home() {
  const { isDark } = useTheme();

  const handleCallMe = () => {
    // Replace with your phone number
    window.open("tel:+1234567890", "_self");
  };

  const handleDownloadCV = () => {
    // Replace with your CV file path
    const cvUrl = "/path-to-your-cv.pdf";
    const link = document.createElement("a");
    link.href = cvUrl;
    link.download = "CV_YourName.pdf";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <section
      id="home"
      className="h-screen dark:bg-black bg-white relative overflow-hidden"
    >
      {/* Background Effects */}
      {isDark ? (
        <Particles
          particleColors={["#ffffff", "#ffffff"]}
          particleCount={200}
          particleSpread={10}
          speed={0.1}
          particleBaseSize={100}
          moveParticlesOnHover={true}
          alphaParticles={false}
          disableRotation={false}
        />
      ) : (
        <Aurora
          colorStops={["#63b6ff", "#1a90ff", "#005db4", "#FFD6E4", "#FFB3B3"]}
          blend={0.5}
          amplitude={1.0}
          speed={0.5}
        />
      )}

      {/* Main Content */}
      <div className="relative z-10 h-full flex items-center justify-center px-6 lg:px-12">
        <div className="max-w-7xl w-full grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Section - Greeting & Content */}
          <div className="text-center lg:text-left space-y-6">
            {/* Greeting */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold">
                <span className="block text-gray-800 dark:text-gray-300 text-2xl md:text-3xl lg:text-4xl font-normal mb-2">
                  Hello, I'm
                </span>
                <span className="bg-gradient-to-r from-primary-light via-primary to-primary-dark bg-clip-text text-transparent">
                  Software Engineer
                </span>
              </h1>

              {/* Description */}
              <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 max-w-2xl leading-relaxed">
                Passionate about creating innovative solutions and building
                amazing digital experiences. I specialize in full-stack
                development with a focus on modern web technologies and
                user-centered design.
              </p>
            </div>

            {/* Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <button
                onClick={handleCallMe}
                className="group relative px-8 py-4 bg-primary hover:bg-primary-dark text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg dark:hover:shadow-primary/25 flex items-center justify-center gap-3"
              >
                <Phone className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
                Call Me
                <div className="absolute inset-0 bg-gradient-to-r from-primary-light to-primary-dark opacity-0 group-hover:opacity-100 rounded-lg blur transition-opacity duration-300 -z-10"></div>
              </button>

              <button
                onClick={handleDownloadCV}
                className="group relative px-8 py-4 border-2 border-primary text-primary hover:bg-primary hover:text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg dark:hover:shadow-primary/25 flex items-center justify-center gap-3"
              >
                <Download className="w-5 h-5 group-hover:translate-y-1 transition-transform duration-300" />
                Download CV
              </button>
            </div>

            {/* Additional Info */}
            <div className="flex flex-wrap gap-4 justify-center lg:justify-start pt-4">
              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                Available for projects
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Based in Your Location
              </div>
            </div>
          </div>

          {/* Right Section - Image */}
          <div className="flex justify-center lg:justify-end">
            <div className="relative group">
              {/* Image Container with Effects */}
              <div className="relative w-80 h-80 lg:w-96 lg:h-96 rounded-2xl overflow-hidden bg-gradient-to-br from-primary-light/20 to-primary-dark/20 backdrop-blur-sm border border-gray-200 dark:border-gray-700">
                <img
                  src={mainImage}
                  alt="Professional headshot"
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl group-hover:scale-110 transition-transform duration-500"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-primary-light/10 rounded-full blur-xl group-hover:scale-110 transition-transform duration-500 delay-100"></div>

              {/* Floating Elements */}
              <div className="absolute top-8 -left-8 w-4 h-4 bg-primary rounded-full animate-bounce delay-1000"></div>
              <div className="absolute bottom-12 -right-6 w-3 h-3 bg-primary-light rounded-full animate-bounce delay-2000"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-gray-400 dark:border-gray-600 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 dark:bg-gray-600 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}

export default Home;
