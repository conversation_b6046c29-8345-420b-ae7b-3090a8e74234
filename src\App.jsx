import React from "react";
import Navbar from "./components/navbar/Navbar";
import Home from "./pages/Home/Home";
import { ThemeProvider } from "./context/ThemeContext";

function App() {
  return (
    <ThemeProvider>
      <Navbar />
      {/* You can add your page sections here */}
      <main>
        <Home />
        <section
          id="about"
          className="min-h-[calc(100vh-64px)] bg-white dark:bg-gray-800"
        >
          About Section
        </section>
        <section
          id="projects"
          className="min-h-[calc(100vh-64px)] bg-gray-100 dark:bg-gray-900"
        >
          Projects Section
        </section>
        <section
          id="skills"
          className="min-h-[calc(100vh-64px)] bg-white dark:bg-gray-800"
        >
          Skills Section
        </section>
        <section
          id="contact"
          className="min-h-[calc(100vh-64px)] bg-gray-100 dark:bg-gray-900"
        >
          Contact Section
        </section>
      </main>
    </ThemeProvider>
  );
}

export default App;
